'use client'

import React, {
  useState, useRef, useEffect, useCallback,
} from 'react';
import Konva from 'konva';
import {
  Stage, Layer,
} from 'react-konva';
import { motion } from 'framer-motion';
import { CanvasSidebar } from './CanvasSidebar';
import { CanvasHeader } from './CanvasHeader';
import { FloatingToolbar } from './FloatingToolbar';
import { LayersPanel } from './LayersPanel';
import { cn } from '@/common/utils/helpers';
import { PLATFORM_CANVAS_SIZES } from '@/common/constants';
import { useProjectContext } from '@/common/contexts/ProjectContext';
import { projectImageStorage } from '@/common/utils/projectImageStorage';
import toast from 'react-hot-toast';

// Simple history management using React state
interface CanvasElement {
  id: string;
  type: 'image' | 'text';
  data: any;
  x: number;
  y: number;
  scaleX?: number;
  scaleY?: number;
  rotation?: number;
  [key: string]: any;
}

interface HistoryState {
  elements: CanvasElement[];
  timestamp: number;
}





export const addCursorHandlers = (node: Konva.Node) => {
  if (!node.draggable()) {
    return;
  }
  node.on('mouseover', function (e) {
    const stage = e.target.getStage();
    if (stage && stage.container()) {
      stage.container().style.cursor = 'move';
    }
  });

  node.on('mouseout', function (e) {
    const stage = e.target.getStage();
    if (stage && stage.container()) {
      stage.container().style.cursor = 'default';
    }
  });

  node.on('dragstart', function (e) {
    const stage = e.target.getStage();
    if (stage && stage.container()) {
      stage.container().style.cursor = 'grabbing';
    }

    const clickedNode = e.target;
    if (clickedNode.getClassName() === 'Transformer') {
      return;
    }

    const layer = clickedNode.getLayer();
    if (!layer) {
      return;
    }
    let transformer = layer.findOne('Transformer') as Konva.Transformer;
    if (!transformer) {
      transformer = new Konva.Transformer();
      layer.add(transformer);
    }
    transformer.nodes([clickedNode]);
    stage?.batchDraw();
  });

  node.on('dragend', function (e) {
    const stage = e.target.getStage();
    if (stage && stage.container()) {
      stage.container().style.cursor = 'move';
    }
  });
};

interface CanvasEditorProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (imageUrl: string) => void;
  initialImage?: string;
  className?: string;
  agentId: string;
  planId: string;
  platform?: string;
}

export const CanvasEditor = ({
  isOpen,
  onClose,
  onSave,
  initialImage,
  className,
  agentId,
  planId,
  platform,
}: CanvasEditorProps) => {
  const stageRef = useRef<Konva.Stage>(null);
  const canvasContainerRef = useRef<HTMLDivElement>(null);
  const [konvaStage, setKonvaStage] = useState<Konva.Stage | null>(null);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [isManualZoom, setIsManualZoom] = useState(false);
  const [canUndo, setCanUndo] = useState(false);
  const [canRedo, setCanRedo] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [showLayers, setShowLayers] = useState(false);
  const [stageSize, setStageSize] = useState<{ width: number; height: number }>(() => {
    const platformKey = platform?.toLowerCase() as keyof typeof PLATFORM_CANVAS_SIZES;
    const canvasSize = PLATFORM_CANVAS_SIZES[platformKey] || PLATFORM_CANVAS_SIZES.default;
    return {
      width: canvasSize.width,
      height: canvasSize.height,
    };
  });

  // Simple history management using React state
  const [history, setHistory] = useState<string[]>([]);
  const [currentHistoryIndex, setCurrentHistoryIndex] = useState(-1);
  const [isRestoringHistory, setIsRestoringHistory] = useState(false);

  const { activeProject } = useProjectContext();

  // History management functions
  const saveToHistory = useCallback(() => {
    if (!konvaStage || isRestoringHistory) {
      return;
    }

    try {
      const stageData = konvaStage.toJSON();

      // Update both history and index in a single state update to avoid race conditions
      setHistory(prev => {
        const newHistory = prev.slice(0, currentHistoryIndex + 1);
        newHistory.push(stageData);

        // Update the index immediately after updating history
        const newIndex = newHistory.length - 1;
        setCurrentHistoryIndex(newIndex);
        setCanUndo(newIndex > 0);
        setCanRedo(false);

        // Limit history size
        if (newHistory.length > 50) {
          const trimmedHistory = newHistory.slice(-50);
          const adjustedIndex = trimmedHistory.length - 1;
          setCurrentHistoryIndex(adjustedIndex);
          setCanUndo(adjustedIndex > 0);
          return trimmedHistory;
        }
        return newHistory;
      });
    } catch (error) {
      console.error('Error saving to history:', error);
    }
  }, [konvaStage, currentHistoryIndex, isRestoringHistory]);

  const restoreFromHistory = useCallback((stageData: string) => {
    if (!konvaStage || !stageData) {
      console.warn('Cannot restore: missing stage or data');
      return;
    }

    setIsRestoringHistory(true);

    try {
      // Get the main layer
      const layer = konvaStage.getLayers()[0];
      if (!layer) {
        console.warn('Cannot restore: no layer found');
        setIsRestoringHistory(false);
        return;
      }

      // Clear current content
      layer.destroyChildren();

      // Parse and restore data
      const parsedData = JSON.parse(stageData);

      if (parsedData.children && parsedData.children[0] && parsedData.children[0].children) {
        parsedData.children[0].children.forEach((nodeData: any) => {
          if (nodeData.className === 'Transformer') {
            return;
          }

          try {
            const node = Konva.Node.create(nodeData);
            if (node) {
              layer.add(node);
              // Re-add event handlers
              if (node.draggable && node.draggable()) {
                addCursorHandlers(node);
              }
            }
          } catch (err) {
            console.error('Error creating node:', err, nodeData);
          }
        });
      }

      // Clear any existing transformers and redraw
      layer.find('Transformer').forEach(t => t.destroy());
      konvaStage.batchDraw();
    } catch (error) {
      console.error('Error restoring from history:', error);
    } finally {
      setIsRestoringHistory(false);
    }
  }, [konvaStage]);

  const updateStageSize = useCallback(() => {
    const platformKey = platform?.toLowerCase() as keyof typeof PLATFORM_CANVAS_SIZES;
    const canvasSize = PLATFORM_CANVAS_SIZES[platformKey] || PLATFORM_CANVAS_SIZES.default;

    setStageSize({
      width: canvasSize.width,
      height: canvasSize.height,
    });
  }, [platform]);

  const handleZoomChange = useCallback((newZoom: number) => {
    setIsManualZoom(true);
    setZoomLevel(newZoom);
  }, []);

  const fitToView = useCallback(() => {
    if (!canvasContainerRef.current) {
      return;
    }

    const platformKey = platform?.toLowerCase() as keyof typeof PLATFORM_CANVAS_SIZES;
    const canvasSize = PLATFORM_CANVAS_SIZES[platformKey] || PLATFORM_CANVAS_SIZES.default;
    const sceneWidth = canvasSize.width;
    const sceneHeight = canvasSize.height;

    const container = canvasContainerRef.current;
    const containerWidth = container.clientWidth - 80;
    const containerHeight = container.clientHeight - 80;

    const scaleX = containerWidth / sceneWidth;
    const scaleY = containerHeight / sceneHeight;
    const newZoom = Math.min(scaleX, scaleY, 1.25);

    setIsManualZoom(false);
    setZoomLevel(newZoom);
  }, [platform]);

  useEffect(() => {
    if (!isOpen) {
      return;
    }
    const handleResize = () => {
      updateStageSize();
    };

    updateStageSize();

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [isOpen, updateStageSize]);

  // Initialize canvas
  useEffect(() => {
    if (!stageRef.current || !isOpen || isInitialized) {
      return;
    }

    const platformKey = platform?.toLowerCase() as keyof typeof PLATFORM_CANVAS_SIZES;
    const canvasSize = PLATFORM_CANVAS_SIZES[platformKey] || PLATFORM_CANVAS_SIZES.default;
    const canvasWidth = canvasSize.width;
    const canvasHeight = canvasSize.height;

    const stage = stageRef.current;
    stage.width(canvasWidth);
    stage.height(canvasHeight);

    setKonvaStage(stage);
    setIsInitialized(true);

    setTimeout(() => {
      if (canvasContainerRef.current) {
        fitToView();
        const container = canvasContainerRef.current;
        const scrollLeft = (container.scrollWidth - container.clientWidth) / 2;
        const scrollTop = (container.scrollHeight - container.clientHeight) / 2;
        container.scrollTo({
          left: scrollLeft,
          top: scrollTop,
          behavior: 'smooth',
        });
      }
    }, 300);

    // Set up event handlers for history tracking
    const handleStateChange = () => {
      if (!isRestoringHistory) {
        setTimeout(() => saveToHistory(), 100);
      }
    };

    stage.on('dragend', handleStateChange);
    stage.on('transformend', handleStateChange);

    if (initialImage) {
      const imageObj = new Image();
      imageObj.crossOrigin = 'anonymous';
      imageObj.onload = () => {
        let layer = stage.findOne('Layer') as Konva.Layer;
        if (!layer) {
          layer = new Konva.Layer();
          stage.add(layer);
        }

        const canvasWidth = stage.width();
        const canvasHeight = stage.height();
        const imgWidth = imageObj.width;
        const imgHeight = imageObj.height;

        const scaleX = canvasWidth / imgWidth;
        const scaleY = canvasHeight / imgHeight;
        const scale = Math.min(scaleX, scaleY);

        const konvaImage = new Konva.Image({
          image: imageObj,
          x: (canvasWidth - imgWidth * scale) / 2,
          y: (canvasHeight - imgHeight * scale) / 2,
          scaleX: scale,
          scaleY: scale,
          draggable: true,
        });

        addCursorHandlers(konvaImage);

        layer.add(konvaImage);
        layer.batchDraw();

        // Save state after adding initial image
        setTimeout(() => saveToHistory(), 100);
      };
      imageObj.src = initialImage;
    }

    return () => {
      stage.off('dragend', handleStateChange);
      stage.off('transformend', handleStateChange);
    };
  }, [isOpen, initialImage, platform, fitToView, isInitialized, saveToHistory, isRestoringHistory]);

  // Separate useEffect for window event listeners
  useEffect(() => {
    if (!isOpen) {
      return;
    }

    // Handle text editing events
    const handleTextEdit = () => {
      if (!isRestoringHistory) {
        setTimeout(() => saveToHistory(), 100);
      }
    };

    // Handle element addition events
    const handleElementAdded = () => {
      if (!isRestoringHistory) {
        setTimeout(() => saveToHistory(), 100);
      }
    };

    window.addEventListener('canvasTextEditComplete', handleTextEdit);
    window.addEventListener('canvasElementAdded', handleElementAdded);

    return () => {
      window.removeEventListener('canvasTextEditComplete', handleTextEdit);
      window.removeEventListener('canvasElementAdded', handleElementAdded);
    };
  }, [isOpen, saveToHistory, isRestoringHistory]);

  useEffect(() => {
    if (!isOpen && isInitialized) {
      // Reset all state when canvas is closed
      setIsInitialized(false);
      setKonvaStage(null);
      setZoomLevel(1);
      setIsManualZoom(false);
      setHistory([]);
      setCurrentHistoryIndex(-1);
      setCanUndo(false);
      setCanRedo(false);
      setShowLayers(false);
      setIsRestoringHistory(false);
    }
  }, [isOpen, isInitialized]);

  const deleteSelectedObjects = useCallback(() => {
    if (!konvaStage) {
      return;
    }

    const transformer = konvaStage.findOne('Transformer') as Konva.Transformer;
    if (!transformer) {
      return;
    }

    const selectedNodes = transformer.nodes();
    if (selectedNodes.length === 0) {
      return;
    }

    selectedNodes.forEach((node) => {
      node.destroy();
    });

    transformer.nodes([]);
    konvaStage.batchDraw();

    // Save state after deletion
    setTimeout(() => saveToHistory(), 100);
  }, [konvaStage, saveToHistory]);

  useEffect(() => {
    if (!isOpen) {
      return;
    }

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Delete' || e.key === 'Backspace') {
        const target = e.target as HTMLElement;
        if (target.tagName !== 'INPUT' && target.tagName !== 'TEXTAREA' && !target.isContentEditable) {
          e.preventDefault();
          deleteSelectedObjects();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, deleteSelectedObjects]);

  const undo = useCallback(() => {
    if (currentHistoryIndex > 0 && history.length > 0) {
      const newIndex = currentHistoryIndex - 1;
      const historyData = history[newIndex];

      if (historyData) {
        setCurrentHistoryIndex(newIndex);
        restoreFromHistory(historyData);
        setCanUndo(newIndex > 0);
        setCanRedo(true);
      }
    }
  }, [currentHistoryIndex, history, restoreFromHistory]);

  const redo = useCallback(() => {
    if (currentHistoryIndex < history.length - 1 && history.length > 0) {
      const newIndex = currentHistoryIndex + 1;
      const historyData = history[newIndex];

      if (historyData) {
        setCurrentHistoryIndex(newIndex);
        restoreFromHistory(historyData);
        setCanUndo(true);
        setCanRedo(newIndex < history.length - 1);
      }
    }
  }, [currentHistoryIndex, history, restoreFromHistory]);

  const handleSaveDesign = async () => {
    if (!konvaStage) {
      console.error('Canvas not available');
      return;
    }

    if (!activeProject?.project_id) {
      console.error('No active project');
      toast.error('No active project found');
      return;
    }

    try {
      const dataURL = konvaStage.toDataURL({
        mimeType: 'image/png',
        quality: 1,
        pixelRatio: 1,
      });

      const response = await fetch(dataURL);
      const blob = await response.blob();
      const timestamp = Date.now();
      const fileName = `canvas-design-${timestamp}.png`;
      const file = new File([blob], fileName, { type: 'image/png' });

      const formData = new FormData();
      formData.append('image', file);
      formData.append('planId', planId || 'canvas-save');

      const baseUrl = process.env.NEXT_PUBLIC_AGENT_URL || 'http://localhost:2151';
      const uploadEndpoint = `${baseUrl}/agents/${agentId}/upload-canvas-image`;

      const uploadResponse = await fetch(uploadEndpoint, {
        method: 'POST',
        body: formData,
      });

      if (!uploadResponse.ok) {
        throw new Error('Failed to upload canvas image');
      }

      const uploadResult = await uploadResponse.json();

      if (uploadResult.success && uploadResult.filepath) {
        await projectImageStorage.addCreationImage(
          activeProject.project_id,
          agentId,
          uploadResult.filepath,
          fileName,
          planId,
          'Canvas Design',
        );

        window.dispatchEvent(new CustomEvent('projectImagesUpdated', {
          detail: { projectId: activeProject.project_id },
        }));

        onSave(uploadResult.filepath);
        toast.success('Canvas design saved successfully!');
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      console.error('Error saving canvas design:', error);
      toast.error('Failed to save canvas design');
      onSave('');
    }
  };

  const handleStageClick = (e: Konva.KonvaEventObject<MouseEvent>) => {
    const stage = stageRef.current;
    if (e.target === stage) {
      const transformer = stage.findOne('Transformer') as Konva.Transformer;
      if (transformer) {
        transformer.nodes([]);
        stage.batchDraw();
      }
      return;
    }

    const clickedNode = e.target;
    if (clickedNode.getClassName() === 'Transformer') {
      return;
    }

    const layer = clickedNode.getLayer();
    if (!layer) {
      return;
    }
    let transformer = layer.findOne('Transformer') as Konva.Transformer;
    if (!transformer) {
      transformer = new Konva.Transformer();
      layer.add(transformer);
    }
    transformer.nodes([clickedNode]);
    stage?.batchDraw();
  };

  // Save initial state when canvas is ready
  useEffect(() => {
    if (konvaStage && isInitialized && history.length === 0) {
      // Save initial empty state
      setTimeout(() => {
        saveToHistory();
      }, 200);
    }
  }, [konvaStage, isInitialized, history.length, saveToHistory]);

  if (!isOpen) {
    return null;
  }
  return (
    <div className={cn(
      "fixed z-50 top-0 left-0 right-0 bottom-0 h-[calc(100vh)] bg-neutral-900 flex flex-col",
      className,
    )}>
      <CanvasHeader
        onSaveDesign={handleSaveDesign}
      />

      <motion.div
        className="flex flex-1 min-h-0 flex-col md:flex-row"
        initial={{ opacity: 0  }}
        animate={{ opacity: 1 }}
        transition={{ 
          duration: 0.6, 
          delay: 1.2,
        }}
      >
        <motion.div
          className="block"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ 
            duration: 0.5, 
            delay: 1.4,
          }}
        >
          <CanvasSidebar
            canvas={konvaStage}
            agentId={agentId}
            planId={planId}
            containerRef={canvasContainerRef}
            zoomLevel={zoomLevel}
            onClose={onClose}
          />
        </motion.div>
        <motion.div
          className="flex-1 bg-neutral-800 flex flex-col"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ 
            duration: 0.6, 
            delay: 1.6,
          }}
        >
          <div
            ref={canvasContainerRef}
            className="flex-1 w-full h-full overflow-auto scroll-smooth relative"
          >
            <div
              className="flex items-center justify-center"
              style={{
                minHeight: `calc(100% + ${Math.max(200, stageSize.height * zoomLevel * 0.5)}px)`,
                minWidth: `calc(100% + ${Math.max(200, stageSize.width * zoomLevel * 0.5)}px)`,
                padding: '100px',
              }}
            >
              <div
                style={{
                  transform: `scale(${zoomLevel})`,
                  transformOrigin: 'center',
                  transition: isManualZoom ? 'none' : 'transform 0.2s ease-out',
                }}
              >
                <Stage
                  ref={stageRef}
                  onClick={handleStageClick}
                  width={stageSize.width}
                  height={stageSize.height}
                  className="block rounded-xl overflow-hidden bg-white shadow-2xl"
                >
                  <Layer />
                </Stage>
              </div>
            </div>

            {/* Layers Panel */}
            {showLayers && (
              <LayersPanel
                canvas={konvaStage}
                onClose={() => setShowLayers(false)}
              />
            )}
          </div>
          <motion.div
            className="text-center text-gray-500 text-xs"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{
              duration: 0.4,
              delay: 2.0,
            }}
          >
            <div className="flex flex-col md:flex-row items-center justify-between gap-2 md:gap-4">
              {/* <p className="text-center md:text-left">Select layer for more options | Double-click text to edit inline | Delete key to remove selected objects</p> */}
              <div></div>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{
                  duration: 0.5,
                  delay: 1.8,
                }}
              >
                <FloatingToolbar
                  canvas={konvaStage}
                  zoomLevel={zoomLevel}
                  onZoomChange={handleZoomChange}
                  onFitToView={fitToView}
                  onUndo={undo}
                  onRedo={redo}
                  canUndo={canUndo}
                  canRedo={canRedo}
                  showLayers={showLayers}
                  onToggleLayers={() => setShowLayers(!showLayers)}
                />
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      </motion.div>
    </div>
  );
};
